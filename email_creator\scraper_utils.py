import random
import re
from typing import Dict, Any

# Shared regexes for contact info and URLs
PHONE_REGEX = re.compile(r"(?:\+?1[-.\s]?)?(?:\(?\d{3}\)?[-.\s]?)?\d{3}[-.\s]?\d{4}")
EMAIL_REGEX = re.compile(r"[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}")
# Basic URL-like detector (avoids matching emails). Keep it simple to reduce false positives.
URL_REGEX = re.compile(r"\b(?:https?://|www\.)[A-Za-z0-9.-]+\.[A-Za-z]{2,}(?:/[A-Za-z0-9._~:/?#\-]*)?\b")


def jitter_delay(min_s: float = 1.5, max_s: float = 4.0) -> float:
    return random.uniform(min_s, max_s)


def _extract_first_website(body: str) -> str:
    # Find first URL-like token that is not part of an email and not a Craigslist URL
    candidates = URL_REGEX.findall(body)
    for c in candidates:
        if not c:
            continue
        if "mailto:" in c:
            continue
        # Normalize to absolute URL
        url = c.strip()
        if url.startswith("www."):
            url = "http://" + url
        # Skip internal Craigslist links
        if "craigslist.org" in url:
            continue
        return url
    return ""


def extract_deterministic(body: str) -> Dict[str, Any]:
    emails = EMAIL_REGEX.findall(body)
    phone = None
    m = PHONE_REGEX.search(body)
    if m:
        phone = m.group(0)
    website = _extract_first_website(body)
    has_website = bool(website)
    return {
        "emails": emails,
        "phone_number": phone or "",
        "has_website": has_website,
        "website": website,
    }

