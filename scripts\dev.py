#!/usr/bin/env python3
"""
Development utility script for the email-creator project.
Provides common development tasks in a single command.
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path


def run_command(cmd, description=""):
    """Run a shell command and handle errors."""
    if description:
        print(f"🔄 {description}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        return False


def clean():
    """Clean up generated files and caches."""
    print("🧹 Cleaning up...")
    
    # Remove Python cache files
    for cache_dir in Path(".").rglob("__pycache__"):
        if cache_dir.is_dir():
            print(f"Removing {cache_dir}")
            import shutil
            shutil.rmtree(cache_dir)
    
    # Remove .pyc files
    for pyc_file in Path(".").rglob("*.pyc"):
        print(f"Removing {pyc_file}")
        pyc_file.unlink()
    
    print("✅ Cleanup complete!")


def test():
    """Run the test suite."""
    print("🧪 Running tests...")
    return run_command("python -m unittest ../tests/test_extractor.py -v", "Running unit tests")


def verify():
    """Verify AI connectivity."""
    print("🔍 Verifying AI connectivity...")
    return run_command("python verify_ai.py", "Testing Hugging Face API connection")


def demo_scrape():
    """Run a demo scrape using local HTML."""
    print("🕷️ Running demo scrape...")
    return run_command("python scrape_craigslist.py --local-html --limit 1", "Demo scrape with local HTML")


def setup():
    """Set up the development environment."""
    print("⚙️ Setting up development environment...")

    # Check if .env exists
    if not Path("../.env").exists():
        print("📝 Creating .env template...")
        with open("../.env", "w") as f:
            f.write("# Add your Hugging Face token here\n")
            f.write("HF_TOKEN=your_token_here\n")
            f.write("# Optional: specify a different model\n")
            f.write("# HF_MODEL=Qwen/Qwen2.5-7B-Instruct\n")
        print("✅ Created .env template - please add your HF_TOKEN")
    
    # Install dependencies
    if not run_command("pip install -r ../requirements.txt", "Installing Python dependencies"):
        return False
    
    # Install Playwright browsers
    if not run_command("python -m playwright install chromium", "Installing Playwright browsers"):
        return False
    
    print("✅ Setup complete!")
    return True


def main():
    parser = argparse.ArgumentParser(description="Development utility for email-creator")
    parser.add_argument("command", choices=["clean", "test", "verify", "demo", "setup"], 
                       help="Command to run")
    
    args = parser.parse_args()
    
    if args.command == "clean":
        clean()
    elif args.command == "test":
        test()
    elif args.command == "verify":
        verify()
    elif args.command == "demo":
        demo_scrape()
    elif args.command == "setup":
        setup()


if __name__ == "__main__":
    main()
