import json
import os
from dotenv import load_dotenv
from huggingface_hub import InferenceClient

load_dotenv()
HF_TOKEN = os.getenv("HF_TOKEN")
MODEL = os.getenv("HF_MODEL", "Qwen/Qwen2.5-7B-Instruct")

SYSTEM = "You are a helpful assistant. Reply with a JSON object: {\"ok\": true, \"model\": \"<model>\"}. No extra text."


def main():
    if not HF_TOKEN:
        print("HF_TOKEN missing in .env")
        return

    client = InferenceClient(token=HF_TOKEN)
    try:
        comp = client.chat.completions.create(
            model=MODEL,
            messages=[
                {"role": "system", "content": SYSTEM},
                {"role": "user", "content": "Say hi."},
            ],
            max_tokens=64,
            temperature=0.0,
        )
        text = comp.choices[0].message.content.strip()
        print("Raw:", text)
        # Attempt JSON parse
        try:
            obj = json.loads(text)
            print("Parsed:", json.dumps(obj, indent=2))
        except Exception:
            print("Note: response was not valid JSON; check model availability or try another model.")
    except Exception as e:
        print("Error calling HF Inference API:", e)


if __name__ == "__main__":
    main()

