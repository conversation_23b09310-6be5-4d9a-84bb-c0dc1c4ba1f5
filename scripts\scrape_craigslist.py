import argparse
import asyncio
import json
import os
import random
import re
import sys
from datetime import datetime
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin

# Add parent directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from dotenv import load_dotenv
from playwright.async_api import async_playwright

# Use the new package structure
from email_creator import analyze_post, build_prospect_record
from email_creator.scraper_utils import (
    jitter_delay,
    extract_deterministic,
    EMAIL_REGEX,
    PHONE_REGEX,
    URL_REGEX,
)

load_dotenv()

DEFAULT_CITY = "chicago"
DEFAULT_CATEGORY = "bbb"  # general services
DEFAULT_LIMIT = 50

# Map Craigslist category identifiers to human-friendly slugs for archive naming
CAT_SLUGS = {
    "bbb": "services",
    "lbs": "labor",
    "bts": "beauty",
    "aos": "automotive",
    "crs": "creative",
    "evs": "event",
    "hss": "household",
    "cps": "computer",
    "fns": "financial",
    "hws": "health",
    "lss": "lessons",
    "sks": "skilled-trade",
}

USER_AGENTS = [
    # A small pool of realistic desktop user agents
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 13_5) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Safari/605.1.15",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0",
]

LANG_HEADERS = ["en-US,en;q=0.9", "en-GB,en;q=0.8"]

LISTING_SELECTOR = "ol.cl-static-search-results li.cl-static-search-result a"
TITLE_SELECTOR = "#titletextonly"
BODY_SELECTOR = "#postingbody"




def build_search_url(city: str, category: str) -> str:
    # Use the canonical search URL without fragile fragment parameters
    return f"https://{city}.craigslist.org/search/{category}"


async def collect_post_links(page, limit: int) -> List[str]:
    """Collect post links from the listing page.
    Strategy:
      1) Try primary selector on the static results list
      2) If empty, scan all anchors ending with .html under craigslist.org
    """
    links: List[str] = []

    # Primary attempt
    try:
        await page.wait_for_selector("ol.cl-static-search-results", state="attached", timeout=12000)
        anchors = await page.query_selector_all(LISTING_SELECTOR)
        for a in anchors:
            href = await a.get_attribute("href")
            if href:
                # Handle either absolute or relative hrefs
                abs_href = href if href.startswith("http") else urljoin(page.url, href)
                if abs_href.startswith("http"):
                    links.append(abs_href)
            if len(links) >= limit:
                break
    except Exception:
        pass

    # Fallback: broad anchors (match any link containing .html, even with query params)
    if not links:
        try:
            await page.wait_for_selector("a[href*='.html']", timeout=15000)
        except Exception:
            # proceed anyway; we will just scan whatever is present
            pass
        more = await page.query_selector_all("a[href*='.html']")
        for a in more:
            href = await a.get_attribute("href")
            if href:
                abs_href = href if href.startswith("http") else urljoin(page.url, href)
                if abs_href.startswith("http") and "craigslist.org" in abs_href:
                    links.append(abs_href)
            if len(links) >= limit:
                break

    # Deduplicate while preserving order
    seen = set()
    deduped = []
    for h in links:
        if h not in seen:
            seen.add(h)
            deduped.append(h)

    return deduped[:limit]






async def parse_post(browser, url: str, model: Optional[str]) -> Optional[Dict[str, Any]]:
    context = await browser.new_context(
        user_agent=random.choice(USER_AGENTS),
        locale="en-US",
        extra_http_headers={
            "Accept-Language": random.choice(LANG_HEADERS),
            "DNT": "1",
            "Upgrade-Insecure-Requests": "1",
        },
        viewport={"width": random.choice([1280, 1366, 1440, 1536, 1920]), "height": random.choice([720, 800, 900, 1080])},
        java_script_enabled=True,
    )
    page = await context.new_page()

    try:
        await page.goto(url, wait_until="domcontentloaded", timeout=25000)
        await asyncio.sleep(jitter_delay())

        # Extract title and body
        await page.wait_for_selector(TITLE_SELECTOR, timeout=15000)
        title_el = await page.query_selector(TITLE_SELECTOR)
        body_el = await page.query_selector(BODY_SELECTOR)
        title = (await title_el.inner_text()).strip() if title_el else ""
        body = (await body_el.inner_text()).strip() if body_el else ""

        # sanitize title/body: remove newlines and common emoji range, trim, and truncate
        def _strip_emojis(s: str) -> str:
            # Remove a few common emoji blocks; keep it simple
            return re.sub(r"[\u2600-\u27BF]", "", s)

        def _cleanup_text(s: str, max_len: int = 1200) -> str:
            s = s.replace("\r", " ").replace("\n", " ")
            s = re.sub(r"\s+", " ", s)
            s = _strip_emojis(s)
            return s.strip()[:max_len]

        title = _cleanup_text(title, 200)
        body = _cleanup_text(body, 2000)

        if not body and not title:
            return None

        # deterministic fields
        det = extract_deterministic(body)

        # infer post id from URL
        post_id_match = re.search(r"/(\d+)\.html", url)
        post_id = post_id_match.group(1) if post_id_match else str(abs(hash(url)))

        # AI analysis
        fields = analyze_post(title, body, url, det, model=model or os.getenv("HF_MODEL"))

        record = build_prospect_record(post_id, url, title, body, fields)
        return record
    except Exception:
        return None
    finally:
        await page.close()
        await context.close()


async def scrape(city: str, category: str, limit: int, model: Optional[str]) -> Dict[str, Any]:
    base_url = build_search_url(city, category)

    async with async_playwright() as pw:
        browser = await pw.chromium.launch(headless=True)
        context = await browser.new_context(
            user_agent=random.choice(USER_AGENTS),
            locale="en-US",
            extra_http_headers={"Accept-Language": random.choice(LANG_HEADERS)},
            java_script_enabled=True,
        )
        page = await context.new_page()

        # Go to listing page
        await page.goto(base_url, wait_until="domcontentloaded", timeout=25000)
        await asyncio.sleep(jitter_delay())

        links = await collect_post_links(page, limit)

        await page.close()
        await context.close()

        # Visit each post separately with its own context to vary fingerprints
        results: List[Dict[str, Any]] = []
        for href in links:
            await asyncio.sleep(jitter_delay(2.0, 6.0))
            rec = await parse_post(browser, href, model)
            if rec is not None:
                results.append(rec)

        await browser.close()

    # Assemble prospects as a mapping of unique IDs to records
    prospects_obj: Dict[str, Any] = {r["id"]: r for r in results}
    # Also provide a top-level list if needed later
    return {
        "meta": {
            "city": city,
            "category": category,
            "limit": limit,
            "count": len(results),
            "scraped_at": datetime.now().astimezone().isoformat(),
        },
        "prospects": prospects_obj,
    }


def save_outputs(payload: Dict[str, Any], city: str, category: str, limit: int):
    # Get the project root directory (parent of scripts directory)
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)
    output_dir = os.path.join(project_root, "output")

    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)

    # Overwrite top-level prospects.json for smart-gen.py compatibility
    top_level = {
        "prospects": payload.get("prospects", {})
    }
    prospects_path = os.path.join(output_dir, "prospects.json")
    with open(prospects_path, "w", encoding="utf-8") as f:
        json.dump(top_level, f, ensure_ascii=False, indent=2)

    # dated archive path
    today = datetime.now().strftime("%m%d%Y")
    folder = os.path.join(output_dir, "scrapes", today)
    os.makedirs(folder, exist_ok=True)

    # human-friendly name; map some popular categories for readability
    cat_human = CAT_SLUGS.get(category, category)
    name = f"{city}-{cat_human}-{limit}.json"
    path = os.path.join(folder, name)
    with open(path, "w", encoding="utf-8") as f:
        json.dump(payload, f, ensure_ascii=False, indent=2)

    return path


async def scrape_local_html(limit: int, model: Optional[str]) -> Dict[str, Any]:
    """Dry-run mode: parse provided local HTML files from data/craigslist_html folder."""
    # Get the project root directory (parent of scripts directory)
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)
    post_path = os.path.join(project_root, "data", "craigslist_html", "actual-post.html")

    # Simulate 1 link repeated up to limit
    links = ["file://" + os.path.abspath(post_path)] * min(limit, 1)

    results: List[Dict[str, Any]] = []
    async with async_playwright() as pw:
        browser = await pw.chromium.launch(headless=True)
        for href in links:
            rec = await parse_post(browser, href, model)
            if rec:
                results.append(rec)
        await browser.close()

    prospects_obj: Dict[str, Any] = {r["id"]: r for r in results}
    return {
        "meta": {"city": "local", "category": "local", "limit": limit, "count": len(results)},
        "prospects": prospects_obj,
    }


def main():
    parser = argparse.ArgumentParser(description="Craigslist scraper for Sonata Sites")
    parser.add_argument("--city", default=DEFAULT_CITY, help="City subdomain, e.g., chicago")
    parser.add_argument("--category", default=DEFAULT_CATEGORY, help="Category identifier, e.g., bbb or lbs")
    parser.add_argument("--limit", type=int, default=DEFAULT_LIMIT, help="Max posts to scrape")
    parser.add_argument("--model", default=os.getenv("HF_MODEL", None), help="HF model id to use for AI extraction")
    parser.add_argument("--local-html", action="store_true", help="Dry-run using local craigslist_html samples")

    args = parser.parse_args()

    if args.local_html:
        payload = asyncio.run(scrape_local_html(args.limit, args.model))
    else:
        payload = asyncio.run(scrape(args.city, args.category, args.limit, args.model))

    outpath = save_outputs(payload, args.city, args.category, args.limit)
    print(f"Saved prospects.json and archive: {outpath}")


if __name__ == "__main__":
    main()

