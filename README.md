# Email Creator - Craigslist Lead Generation Tool

A Python-based tool that scrapes Craigslist for potential business leads and generates personalized outreach emails using AI.

## Overview

This project consists of two main phases:

1. **Scraping Phase**: Scrapes Craigslist posts and extracts business information using AI
2. **Email Generation Phase**: Creates personalized outreach emails for potential clients

## Project Structure

```
email-creator/
├── scripts/                # Executable scripts
│   ├── scrape_craigslist.py   # Main scraping script
│   ├── smart-gen.py           # Email generation script
│   ├── verify_ai.py           # AI connectivity test
│   └── dev.py                 # Development utility
├── email_creator/          # Core package
│   ├── __init__.py            # Package exports
│   ├── extractor.py           # AI extraction and field merging
│   └── scraper_utils.py       # Utility functions and regex patterns
├── data/                   # Sample data and examples
│   ├── craigslist_html/       # Sample HTML files for testing
│   └── example/               # Example data files
├── output/                 # Generated files (gitignored)
│   ├── prospects.json         # Current prospects for email generation
│   ├── emails/                # Generated email files
│   └── scrapes/               # Backup scrapes by date
├── tests/                  # Unit tests
├── docs/                   # Documentation
└── config files            # .env, requirements.txt, etc.
```

## Quick Start

1. **Setup:**
   ```bash
   cd scripts
   python dev.py setup
   ```

2. **Test AI connectivity:**
   ```bash
   python verify_ai.py
   ```

3. **Run demo scrape:**
   ```bash
   python scrape_craigslist.py --local-html --limit 1
   ```

4. **Generate emails:**
   ```bash
   python smart-gen.py
   ```

## Configuration

Create `.env` file in the root directory:
```
HF_TOKEN=your_huggingface_token
HF_MODEL=Qwen/Qwen2.5-7B-Instruct  # optional
```

## Usage

All scripts are located in the `scripts/` directory. Run them from there:

```bash
cd scripts

# Scrape live Craigslist
python scrape_craigslist.py --city chicago --category bbb --limit 25

# Generate emails from prospects
python smart-gen.py

# Development utilities
python dev.py test     # Run tests
python dev.py clean    # Clean cache files
python dev.py verify   # Test AI connection
```

## Category Codes

- `bbb` - General services
- `lbs` - Labor/moving  
- `bts` - Beauty/wellness
- `aos` - Automotive
- `crs` - Creative services
- `evs` - Event services

## Documentation

See `docs/` folder for detailed documentation and commands.

## License

This project is for educational and business development purposes.
