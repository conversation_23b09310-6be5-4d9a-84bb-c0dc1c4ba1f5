# Email Creator - Craigslist Lead Generation Tool

A Python-based tool that scrapes Craigslist for potential business leads and generates personalized outreach emails using AI.

## Overview

This project consists of two main phases:

1. **Scraping Phase**: Scrapes Craigslist posts and extracts business information using AI
2. **Email Generation Phase**: Creates personalized outreach emails for potential clients

## Project Structure

```
email-creator/
├── email_creator/           # Core package
│   ├── __init__.py         # Package exports
│   ├── extractor.py        # AI extraction and field merging
│   └── scraper_utils.py    # Utility functions and regex patterns
├── scrape_craigslist.py    # Main scraping script
├── smart-gen.py            # Email generation script
├── verify_ai.py            # AI connectivity test
├── tests/                  # Unit tests
├── craigslist_html/        # Sample HTML files for testing
├── example/                # Example data files
├── prospects.json          # Generated prospects (gitignored)
├── emails/                 # Generated emails (gitignored)
├── scrapes/                # Backup scrapes by date (gitignored)
└── requirements.txt        # Python dependencies
```

## Setup

1. **Create and configure `.env` file:**
   ```
   HF_TOKEN=your_huggingface_token
   HF_MODEL=Qwen/Qwen2.5-7B-Instruct  # optional
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Install Playwright browsers (first time only):**
   ```bash
   python -m playwright install chromium
   ```

## Usage

### 1. Verify AI Connectivity
```bash
python verify_ai.py
```

### 2. Scrape Craigslist (Live)
```bash
python scrape_craigslist.py --city chicago --category bbb --limit 25
```

### 3. Scrape (Dry-run using local HTML)
```bash
python scrape_craigslist.py --local-html --limit 1
```

### 4. Generate Emails
```bash
python smart-gen.py
```

## Output Files

- `prospects.json` - Current prospects for email generation
- `scrapes/MMDDYYYY/` - Dated backup archives
- `emails/` - Generated email files

## Category Codes

Common Craigslist category identifiers:
- `bbb` - General services
- `lbs` - Labor/moving
- `bts` - Beauty/wellness
- `aos` - Automotive
- `crs` - Creative services
- `evs` - Event services

## Testing

Run the test suite:
```bash
python -m unittest tests/test_extractor.py
```

## Development

The project uses a clean package structure. Import from `email_creator`:

```python
from email_creator import analyze_post, build_prospect_record
from email_creator.scraper_utils import extract_deterministic
```

## License

This project is for educational and business development purposes.
